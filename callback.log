2025-08-03 19:46:57,803 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8886
 * Running on http://************:8886
2025-08-03 19:46:57,803 - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 19:49:01,063 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8886
 * Running on http://************:8886
2025-08-03 19:49:01,063 - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 19:50:31,369 - INFO - *************** - - [03/Aug/2025 19:50:31] "[31m[1mGET /notify?pid=1028&trade_no=2025080319493529461&out_trade_no=1754221775473&type=alipay&name=测试商品-支付宝&money=0.01&trade_status=TRADE_SUCCESS&sign=0c006368ca3664fc2dd3b5a3b060ec67&sign_type=MD5 HTTP/1.1[0m" 405 -
2025-08-03 19:51:22,946 - INFO - === 收到同步返回 ===
2025-08-03 19:51:44,717 - INFO - *************** - - [03/Aug/2025 19:51:44] "[31m[1mGET /notify?pid=1028&trade_no=2025080319493679082&out_trade_no=1754221776011&type=wxpay&name=测试商品-微信&money=0.01&trade_status=TRADE_SUCCESS&sign=06265e8db92141c3627c0ef16f654f4f&sign_type=MD5 HTTP/1.1[0m" 405 -
2025-08-03 19:51:48,612 - INFO - 返回数据: {
  "pid": "1028",
  "trade_no": "2025080319493529461",
  "out_trade_no": "1754221775473",
  "type": "alipay",
  "name": "测试商品-支付宝",
  "money": "0.01",
  "trade_status": "TRADE_SUCCESS",
  "sign": "0c006368ca3664fc2dd3b5a3b060ec67",
  "sign_type": "MD5"
}
2025-08-03 19:51:48,612 - INFO - ✅ 同步返回签名验证通过
2025-08-03 19:51:48,613 - INFO - ************** - - [03/Aug/2025 19:51:48] "GET /return?pid=1028&trade_no=2025080319493529461&out_trade_no=1754221775473&type=alipay&name=测试商品-支付宝&money=0.01&trade_status=TRADE_SUCCESS&sign=0c006368ca3664fc2dd3b5a3b060ec67&sign_type=MD5 HTTP/1.1" 200 -
2025-08-03 19:53:22,111 - INFO - ************ - - [03/Aug/2025 19:53:22] "[33mGET /status HTTP/1.1[0m" 404 -
2025-08-03 19:53:23,088 - INFO - ************ - - [03/Aug/2025 19:53:23] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
