#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回调服务器测试脚本
用于测试回调服务器的各种功能
"""

import requests
import hashlib
import time
import json

# 配置
CALLBACK_URL = "http://turtle.155031.xyz:8886"
PAYMENT_CONFIG = {
    'pid': '1028',
    'md5_key': '53X2ZAY3xacC3yD2diHih27xVxvxcfid'
}

def generate_sign(params):
    """生成MD5签名"""
    # 排除sign和sign_type字段，以及空值
    filtered_params = {}
    for k, v in params.items():
        if k != 'sign' and k != 'sign_type' and str(v) != '':
            filtered_params[k] = v
    
    # 按键名排序
    sorted_params = sorted(filtered_params.items())
    
    # 拼接参数字符串
    sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
    
    # 拼接密钥
    sign_str += PAYMENT_CONFIG['md5_key']
    
    # 生成MD5签名
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest()

def test_health_check():
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    try:
        response = requests.get(f"{CALLBACK_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False

def test_index_page():
    """测试首页"""
    print("\n=== 测试首页 ===")
    try:
        response = requests.get(f"{CALLBACK_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 首页访问正常")
            return True
        else:
            print(f"❌ 首页访问失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 首页访问异常: {str(e)}")
        return False

def test_notify_callback():
    """测试异步通知回调"""
    print("\n=== 测试异步通知回调 ===")
    
    # 模拟支付成功的通知数据
    notify_data = {
        'pid': PAYMENT_CONFIG['pid'],
        'trade_no': '2025080319999999999',
        'out_trade_no': str(int(time.time() * 1000)),
        'type': 'alipay',
        'name': '测试商品',
        'money': '0.01',
        'trade_status': 'TRADE_SUCCESS'
    }
    
    # 生成签名
    notify_data['sign'] = generate_sign(notify_data)
    
    print(f"发送通知数据: {json.dumps(notify_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(f"{CALLBACK_URL}/notify", data=notify_data, timeout=5)
        if response.status_code == 200:
            result = response.text
            print(f"✅ 异步通知响应: {result}")
            return result == "success"
        else:
            print(f"❌ 异步通知失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 异步通知异常: {str(e)}")
        return False

def test_return_callback():
    """测试同步返回回调"""
    print("\n=== 测试同步返回回调 ===")
    
    # 模拟支付成功的返回数据
    return_data = {
        'pid': PAYMENT_CONFIG['pid'],
        'trade_no': '2025080319999999999',
        'out_trade_no': str(int(time.time() * 1000)),
        'type': 'wxpay',
        'name': '测试商品',
        'money': '0.01',
        'trade_status': 'TRADE_SUCCESS'
    }
    
    # 生成签名
    return_data['sign'] = generate_sign(return_data)
    
    print(f"发送返回数据: {json.dumps(return_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.get(f"{CALLBACK_URL}/return", params=return_data, timeout=5)
        if response.status_code == 200:
            print("✅ 同步返回响应正常")
            print(f"响应长度: {len(response.text)} 字符")
            return True
        else:
            print(f"❌ 同步返回失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 同步返回异常: {str(e)}")
        return False

def test_status_query():
    """测试状态查询"""
    print("\n=== 测试状态查询 ===")
    
    # 先发送一个通知，然后查询状态
    out_trade_no = str(int(time.time() * 1000))
    
    notify_data = {
        'pid': PAYMENT_CONFIG['pid'],
        'trade_no': '2025080319888888888',
        'out_trade_no': out_trade_no,
        'type': 'qqpay',
        'name': '测试商品-状态查询',
        'money': '0.02',
        'trade_status': 'TRADE_SUCCESS'
    }
    
    notify_data['sign'] = generate_sign(notify_data)
    
    # 发送通知
    try:
        notify_response = requests.post(f"{CALLBACK_URL}/notify", data=notify_data, timeout=5)
        if notify_response.text == "success":
            print("✅ 通知发送成功，开始查询状态")
            
            # 查询状态
            status_response = requests.get(f"{CALLBACK_URL}/status/{out_trade_no}", timeout=5)
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"✅ 状态查询成功: {json.dumps(status_data, indent=2, ensure_ascii=False)}")
                return status_data.get('status') == 'success'
            else:
                print(f"❌ 状态查询失败: HTTP {status_response.status_code}")
                return False
        else:
            print("❌ 通知发送失败，无法测试状态查询")
            return False
    except Exception as e:
        print(f"❌ 状态查询异常: {str(e)}")
        return False

def test_invalid_signature():
    """测试无效签名"""
    print("\n=== 测试无效签名 ===")
    
    notify_data = {
        'pid': PAYMENT_CONFIG['pid'],
        'trade_no': '2025080319777777777',
        'out_trade_no': str(int(time.time() * 1000)),
        'type': 'alipay',
        'name': '测试商品',
        'money': '0.01',
        'trade_status': 'TRADE_SUCCESS',
        'sign': 'invalid_signature_12345'  # 无效签名
    }
    
    try:
        response = requests.post(f"{CALLBACK_URL}/notify", data=notify_data, timeout=5)
        if response.status_code == 200:
            result = response.text
            print(f"✅ 无效签名测试响应: {result}")
            return result == "fail"
        else:
            print(f"❌ 无效签名测试失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无效签名测试异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=== 回调服务器功能测试 ===")
    print(f"测试目标: {CALLBACK_URL}")
    print("=" * 50)
    
    tests = [
        ("健康检查", test_health_check),
        ("首页访问", test_index_page),
        ("异步通知", test_notify_callback),
        ("同步返回", test_return_callback),
        ("状态查询", test_status_query),
        ("无效签名", test_invalid_signature),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {str(e)}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("=== 测试结果汇总 ===")
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！回调服务器运行正常！")
    else:
        print("⚠️  部分测试失败，请检查服务器状态")

if __name__ == "__main__":
    main()
