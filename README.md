# 支付接口测试与回调服务器

## 项目概述

这是一个完整的支付接口测试和回调处理系统，包含：
- 支付接口调用测试
- 回调服务器（处理异步通知和同步返回）
- 支付状态查询API

## 文件说明

- `testapi.py` - 支付接口测试脚本（包含回调服务器功能）
- `callback_server.py` - 独立的回调服务器
- `start_server.bat` - Windows启动脚本
- `支付接口测试结果.md` - 详细测试报告

## 快速开始

### 1. 安装依赖

```bash
pip install requests flask
```

### 2. 测试支付接口

```bash
python testapi.py
```

### 3. 启动回调服务器

**方式一：使用独立服务器**
```bash
python callback_server.py
```

**方式二：使用测试脚本启动**
```bash
python testapi.py server
```

**方式三：Windows用户**
```bash
start_server.bat
```

## 回调服务器功能

### API端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/` | GET | 服务器首页和状态 |
| `/notify` | POST | 异步通知回调 |
| `/return` | GET | 同步返回回调 |
| `/status/<订单号>` | GET | 查询支付状态 |
| `/health` | GET | 健康检查 |

### 回调地址配置

- **服务器地址**: `http://turtle.155031.xyz:8886`
- **异步通知**: `http://turtle.155031.xyz:8886/notify`
- **同步返回**: `http://turtle.155031.xyz:8886/return`

## 支付接口配置

```python
PAYMENT_CONFIG = {
    'api_url': 'https://epay.ovovps.cn/mapi.php',
    'pid': '1028',
    'md5_key': '53X2ZAY3xacC3yD2diHih27xVxvxcfid',
    'callback_url': 'http://turtle.155031.xyz:8886'
}
```

## 支持的支付方式

- ✅ **alipay** - 支付宝
- ✅ **wxpay** - 微信支付
- ✅ **qqpay** - QQ钱包

## 使用示例

### 创建支付订单

```python
from testapi import PaymentAPI

# 初始化API
api = PaymentAPI()

# 创建支付宝订单
result = api.create_payment("alipay", "0.01", "测试商品")

if result and result.get('code') == 1:
    print(f"订单创建成功！")
    print(f"交易号: {result.get('trade_no')}")
    print(f"支付链接: https://epay.ovovps.cn{result.get('payurl')}")
```

### 查询支付状态

```bash
curl http://turtle.155031.xyz:8886/status/1754221087766
```

响应示例：
```json
{
  "status": "success",
  "trade_no": "2025080319380666884",
  "out_trade_no": "1754221087766",
  "money": "0.01",
  "type": "alipay",
  "timestamp": 1754221087.123
}
```

## 签名算法

按照官方PHP SDK实现的MD5签名算法：

1. 排除 `sign` 和 `sign_type` 字段
2. 排除空值字段
3. 按键名进行字典序排序
4. 用 `&` 连接参数：`key1=value1&key2=value2`
5. 直接拼接密钥（不加 `&key=`）
6. 对整个字符串进行MD5加密

## 回调处理流程

### 异步通知 (POST /notify)

1. 接收POST数据
2. 验证MD5签名
3. 存储支付结果
4. 返回 "success" 或 "fail"
5. 记录日志

### 同步返回 (GET /return)

1. 接收GET参数
2. 验证MD5签名
3. 显示支付结果页面
4. 记录日志

## 日志记录

回调服务器会自动记录所有操作到：
- 控制台输出
- `callback.log` 文件

## 安全注意事项

1. **签名验证**: 所有回调都会进行严格的MD5签名验证
2. **日志记录**: 详细记录所有操作，便于排查问题
3. **错误处理**: 完善的异常处理机制

## 测试结果

最新测试结果（2025-08-03）：

- ✅ 支付宝支付：成功
- ✅ 微信支付：成功  
- ✅ QQ支付：成功
- ✅ 签名算法：验证通过
- ✅ 回调服务器：正常运行

## 故障排除

### 常见问题

1. **签名验证失败**
   - 检查密钥是否正确
   - 确认签名算法实现
   - 查看日志中的签名字符串

2. **网络连接问题**
   - 检查防火墙设置
   - 确认端口8886是否开放
   - 测试服务器连通性

3. **回调未收到**
   - 确认回调地址配置正确
   - 检查服务器是否正常运行
   - 查看服务器日志

### 调试命令

```bash
# 测试服务器连通性
curl http://turtle.155031.xyz:8886/health

# 查看服务器状态
curl http://turtle.155031.xyz:8886/

# 测试支付接口
python testapi.py
```

## 联系信息

如有问题，请查看日志文件或联系技术支持。

---
*最后更新: 2025-08-03*
