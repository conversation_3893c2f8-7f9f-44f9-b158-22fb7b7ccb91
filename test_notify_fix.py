#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试异步通知修复
模拟支付接口发送GET请求到/notify端点
"""

import requests
import hashlib
import time
import json

# 配置
CALLBACK_URL = "http://turtle.155031.xyz:8886"
PAYMENT_CONFIG = {
    'pid': '1028',
    'md5_key': '53X2ZAY3xacC3yD2diHih27xVxvxcfid'
}

def generate_sign(params):
    """生成MD5签名"""
    # 排除sign和sign_type字段，以及空值
    filtered_params = {}
    for k, v in params.items():
        if k != 'sign' and k != 'sign_type' and str(v) != '':
            filtered_params[k] = v
    
    # 按键名排序
    sorted_params = sorted(filtered_params.items())
    
    # 拼接参数字符串
    sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
    
    # 拼接密钥
    sign_str += PAYMENT_CONFIG['md5_key']
    
    # 生成MD5签名
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest()

def test_get_notify():
    """测试GET方式的异步通知"""
    print("=== 测试GET方式异步通知 ===")
    
    # 模拟支付成功的通知数据
    notify_data = {
        'pid': PAYMENT_CONFIG['pid'],
        'trade_no': '2025080319999888777',
        'out_trade_no': str(int(time.time() * 1000)),
        'type': 'alipay',
        'name': '测试商品-GET通知',
        'money': '0.01',
        'trade_status': 'TRADE_SUCCESS',
        'sign_type': 'MD5'
    }
    
    # 生成签名
    notify_data['sign'] = generate_sign(notify_data)
    
    print(f"发送GET通知数据: {json.dumps(notify_data, indent=2, ensure_ascii=False)}")
    
    try:
        # 使用GET方法发送请求（模拟支付接口的行为）
        response = requests.get(f"{CALLBACK_URL}/notify", params=notify_data, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.text.strip()
            if result == "success":
                print("✅ GET方式异步通知处理成功！")
                return True
            else:
                print(f"❌ GET方式异步通知处理失败: {result}")
                return False
        else:
            print(f"❌ GET方式异步通知HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ GET方式异步通知异常: {str(e)}")
        return False

def test_post_notify():
    """测试POST方式的异步通知"""
    print("\n=== 测试POST方式异步通知 ===")
    
    # 模拟支付成功的通知数据
    notify_data = {
        'pid': PAYMENT_CONFIG['pid'],
        'trade_no': '2025080319999777666',
        'out_trade_no': str(int(time.time() * 1000)),
        'type': 'wxpay',
        'name': '测试商品-POST通知',
        'money': '0.02',
        'trade_status': 'TRADE_SUCCESS',
        'sign_type': 'MD5'
    }
    
    # 生成签名
    notify_data['sign'] = generate_sign(notify_data)
    
    print(f"发送POST通知数据: {json.dumps(notify_data, indent=2, ensure_ascii=False)}")
    
    try:
        # 使用POST方法发送请求
        response = requests.post(f"{CALLBACK_URL}/notify", data=notify_data, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.text.strip()
            if result == "success":
                print("✅ POST方式异步通知处理成功！")
                return True
            else:
                print(f"❌ POST方式异步通知处理失败: {result}")
                return False
        else:
            print(f"❌ POST方式异步通知HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ POST方式异步通知异常: {str(e)}")
        return False

def test_server_status():
    """测试服务器状态"""
    print("=== 检查服务器状态 ===")
    try:
        response = requests.get(f"{CALLBACK_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器运行正常: {data.get('status')}")
            return True
        else:
            print(f"❌ 服务器状态异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务器: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=== 异步通知修复测试 ===")
    print(f"测试目标: {CALLBACK_URL}")
    print("=" * 50)
    
    # 检查服务器状态
    if not test_server_status():
        print("❌ 服务器未运行，请先启动回调服务器")
        return
    
    print()
    
    # 测试GET和POST方式
    get_result = test_get_notify()
    post_result = test_post_notify()
    
    print("\n" + "=" * 50)
    print("=== 测试结果 ===")
    print(f"GET方式异步通知: {'✅ 通过' if get_result else '❌ 失败'}")
    print(f"POST方式异步通知: {'✅ 通过' if post_result else '❌ 失败'}")
    
    if get_result and post_result:
        print("\n🎉 异步通知修复成功！现在支持GET和POST两种方式！")
    elif get_result:
        print("\n✅ GET方式正常，这是支付接口使用的方式")
    else:
        print("\n❌ 异步通知仍有问题，请检查服务器日志")

if __name__ == "__main__":
    main()
