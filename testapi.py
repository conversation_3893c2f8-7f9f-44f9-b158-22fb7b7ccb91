
import requests
import hashlib
import time
import json
from flask import Flask, request, jsonify
import threading

class PaymentAPI:
    def __init__(self, callback_url="http://turtle.155031.xyz:8886"):
        self.api_url = "https://epay.ovovps.cn/mapi.php"
        self.pid = "1028"
        self.md5_key = "53X2ZAY3xacC3yD2diHih27xVxvxcfid"
        self.callback_url = callback_url
    
    def generate_sign(self, params):
        """生成MD5签名 - 按照官方PHP SDK的算法"""
        # 排除sign和sign_type字段，以及空值
        filtered_params = {}
        for k, v in params.items():
            if k != 'sign' and k != 'sign_type' and str(v) != '':
                filtered_params[k] = v

        # 按键名排序
        sorted_params = sorted(filtered_params.items())

        # 拼接参数字符串
        sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])

        # 拼接密钥
        sign_str += self.md5_key

        print(f"签名字符串: {sign_str}")

        # 生成MD5签名
        md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        print(f"生成的MD5签名: {md5_hash}")

        return md5_hash
    
    def create_payment(self, pay_type="alipay", money="1.00", name="测试商品"):
        """创建支付订单"""
        # 生成订单号
        out_trade_no = str(int(time.time() * 1000))

        # 构建请求参数
        params = {
            "pid": self.pid,
            "type": pay_type,
            "out_trade_no": out_trade_no,
            "notify_url": f"{self.callback_url}/notify",
            "return_url": f"{self.callback_url}/return",
            "name": name,
            "money": money,
            "clientip": "*************",
            "device": "pc",
            "param": "",
            "sign_type": "MD5"
        }

        # 生成签名
        params["sign"] = self.generate_sign(params)

        print(f"请求参数: {json.dumps(params, indent=2, ensure_ascii=False)}")

        try:
            # 发送POST请求，禁用代理
            response = requests.post(
                self.api_url,
                data=params,
                timeout=30,
                proxies={'http': None, 'https': None}  # 禁用代理
            )

            print(f"HTTP状态码: {response.status_code}")
            print(f"原始响应内容: {response.text}")

            # 检查响应状态
            if response.status_code != 200:
                print(f"HTTP请求失败，状态码: {response.status_code}")
                return None

            # 尝试解析JSON
            try:
                result = response.json()
                print(f"解析后的JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
            except json.JSONDecodeError as json_error:
                print(f"JSON解析失败: {json_error}")
                print(f"响应内容不是有效的JSON: {response.text}")
                return None

        except requests.exceptions.RequestException as req_error:
            print(f"网络请求失败: {req_error}")
            return None
        except Exception as e:
            print(f"其他错误: {str(e)}")
            return None

# 创建Flask应用
app = Flask(__name__)

# 存储支付结果的字典
payment_results = {}

class CallbackServer:
    def __init__(self, payment_api):
        self.payment_api = payment_api

    def start_server(self, host='0.0.0.0', port=8886):
        """启动回调服务器"""
        global payment_api_instance
        payment_api_instance = self.payment_api
        print(f"启动回调服务器: http://{host}:{port}")
        print(f"异步通知地址: http://{host}:{port}/notify")
        print(f"同步返回地址: http://{host}:{port}/return")
        app.run(host=host, port=port, debug=False)

# 全局变量存储PaymentAPI实例
payment_api_instance = None

@app.route('/notify', methods=['POST'])
def notify_callback():
    """异步通知回调"""
    try:
        # 获取POST数据
        data = request.form.to_dict()

        print("=== 收到异步通知 ===")
        print(f"通知数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

        if payment_api_instance:
            # 验证签名
            if payment_api_instance.generate_sign(data) == data.get('sign'):
                print("✅ 签名验证通过")

                # 存储支付结果
                trade_no = data.get('trade_no')
                out_trade_no = data.get('out_trade_no')

                payment_results[out_trade_no] = {
                    'status': 'success',
                    'trade_no': trade_no,
                    'money': data.get('money'),
                    'type': data.get('type'),
                    'timestamp': time.time(),
                    'raw_data': data
                }

                print(f"✅ 支付成功！订单号: {out_trade_no}, 交易号: {trade_no}")
                return "success"
            else:
                print("❌ 签名验证失败")
                return "fail"
        else:
            print("❌ PaymentAPI实例未初始化")
            return "fail"

    except Exception as e:
        print(f"❌ 处理异步通知时出错: {str(e)}")
        return "fail"

@app.route('/return', methods=['GET'])
def return_callback():
    """同步返回回调"""
    try:
        # 获取GET参数
        data = request.args.to_dict()

        print("=== 收到同步返回 ===")
        print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

        if payment_api_instance:
            # 验证签名
            if payment_api_instance.generate_sign(data) == data.get('sign'):
                print("✅ 同步返回签名验证通过")

                trade_no = data.get('trade_no')
                out_trade_no = data.get('out_trade_no')

                return f"""
                <html>
                <head><title>支付结果</title></head>
                <body>
                    <h2>支付成功！</h2>
                    <p>订单号: {out_trade_no}</p>
                    <p>交易号: {trade_no}</p>
                    <p>支付金额: {data.get('money')} 元</p>
                    <p>支付方式: {data.get('type')}</p>
                    <p><a href="javascript:window.close()">关闭窗口</a></p>
                </body>
                </html>
                """
            else:
                print("❌ 同步返回签名验证失败")
                return "<h2>签名验证失败</h2>"
        else:
            return "<h2>系统错误</h2>"

    except Exception as e:
        print(f"❌ 处理同步返回时出错: {str(e)}")
        return f"<h2>处理错误: {str(e)}</h2>"

@app.route('/status/<out_trade_no>', methods=['GET'])
def check_payment_status(out_trade_no):
    """查询支付状态"""
    if out_trade_no in payment_results:
        return jsonify(payment_results[out_trade_no])
    else:
        return jsonify({'status': 'pending', 'message': '支付未完成或订单不存在'})

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({'status': 'ok', 'timestamp': time.time()})

# 测试代码
if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == 'server':
        # 启动回调服务器模式
        api = PaymentAPI()
        callback_server = CallbackServer(api)
        print("=== 启动回调服务器模式 ===")
        print(f"回调地址: {api.callback_url}")
        callback_server.start_server()
    else:
        # 测试支付接口模式
        api = PaymentAPI()

        print("=== 支付接口测试 ===")
        print("API地址:", api.api_url)
        print("商户ID:", api.pid)
        print("密钥:", api.md5_key[:8] + "..." + api.md5_key[-8:])
        print("回调地址:", api.callback_url)
        print()

    # 测试支付宝支付
    print("=== 测试支付宝支付 ===")
    result_alipay = api.create_payment("alipay", "0.01", "测试商品-支付宝")
    if result_alipay and result_alipay.get('code') == 1:
        print(f"✅ 支付宝支付订单创建成功！")
        print(f"   交易号: {result_alipay.get('trade_no')}")
        payurl = result_alipay.get('payurl')
        if not payurl.startswith('http'):
            payurl = f"https://epay.ovovps.cn{payurl}"
        print(f"   支付链接: {payurl}")
    else:
        print("❌ 支付宝支付订单创建失败")

    print()

    # 测试微信支付
    print("=== 测试微信支付 ===")
    result_wxpay = api.create_payment("wxpay", "0.01", "测试商品-微信")
    if result_wxpay and result_wxpay.get('code') == 1:
        print(f"✅ 微信支付订单创建成功！")
        print(f"   交易号: {result_wxpay.get('trade_no')}")
        payurl = result_wxpay.get('payurl')
        if not payurl.startswith('http'):
            payurl = f"https://epay.ovovps.cn{payurl}"
        print(f"   支付链接: {payurl}")
    else:
        print("❌ 微信支付订单创建失败")

    print()

    # 测试QQ支付
    print("=== 测试QQ支付 ===")
    result_qqpay = api.create_payment("qqpay", "0.01", "测试商品-QQ")
    if result_qqpay and result_qqpay.get('code') == 1:
        print(f"✅ QQ支付订单创建成功！")
        print(f"   交易号: {result_qqpay.get('trade_no')}")
        payurl = result_qqpay.get('payurl')
        if not payurl.startswith('http'):
            payurl = f"https://epay.ovovps.cn{payurl}"
        print(f"   支付链接: {payurl}")
    else:
        print("❌ QQ支付订单创建失败")

    print()
    print("=== 测试总结 ===")
    success_count = 0
    if result_alipay and result_alipay.get('code') == 1:
        success_count += 1
    if result_wxpay and result_wxpay.get('code') == 1:
        success_count += 1
    if result_qqpay and result_qqpay.get('code') == 1:
        success_count += 1

        print(f"成功创建 {success_count}/3 个支付订单")
        print("签名算法验证通过！✅")
        print()
        print("=== 启动回调服务器 ===")
        print("要启动回调服务器来接收支付通知，请运行:")
        print("python testapi.py server")
        print()
        print("回调服务器将监听以下地址:")
        print(f"- 异步通知: {api.callback_url}/notify")
        print(f"- 同步返回: {api.callback_url}/return")
        print(f"- 状态查询: {api.callback_url}/status/<订单号>")
        print(f"- 健康检查: {api.callback_url}/health")

