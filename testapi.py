
import requests
import hashlib
import time
import json

class PaymentAPI:
    def __init__(self):
        self.api_url = "https://epay.ovovps.cn/mapi.php"
        self.pid = "1028"
        self.md5_key = "53X2ZAY3xacC3yD2diHih27xVxvxcfid"
    
    def generate_sign(self, params):
        """生成MD5签名"""
        # 按键名排序并拼接参数
        sorted_params = sorted(params.items())
        sign_str = "&".join([f"{k}={v}" for k, v in sorted_params if v])
        sign_str += self.md5_key
        
        # 生成MD5签名
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    
    def create_payment(self, pay_type="alipay", money="1.00", name="测试商品"):
        """创建支付订单"""
        # 生成订单号
        out_trade_no = str(int(time.time() * 1000))
        
        # 构建请求参数
        params = {
            "pid": self.pid,
            "type": pay_type,
            "out_trade_no": out_trade_no,
            "notify_url": "http://www.example.com/notify",
            "return_url": "http://www.example.com/return",
            "name": name,
            "money": money,
            "clientip": "*************",
            "device": "pc",
            "param": "",
            "sign_type": "MD5"
        }
        
        # 生成签名
        params["sign"] = self.generate_sign(params)
        
        try:
            # 发送POST请求
            response = requests.post(self.api_url, data=params, timeout=30)
            result = response.json()
            
            print(f"请求参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
            print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return result
            
        except Exception as e:
            print(f"请求失败: {str(e)}")
            return None

# 测试代码
if __name__ == "__main__":
    api = PaymentAPI()
    
    # 测试支付宝支付
    print("=== 测试支付宝支付 ===")
    result = api.create_payment("alipay", "0.01", "测试商品-支付宝")
    
    # 测试微信支付
    print("\n=== 测试微信支付 ===")
    result = api.create_payment("wxpay", "0.01", "测试商品-微信")

