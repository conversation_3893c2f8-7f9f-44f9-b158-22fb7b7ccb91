
import requests
import hashlib
import time
import json

class PaymentAPI:
    def __init__(self):
        self.api_url = "https://epay.ovovps.cn/mapi.php"
        self.pid = "1028"
        self.md5_key = "53X2ZAY3xacC3yD2diHih27xVxvxcfid"
    
    def generate_sign(self, params):
        """生成MD5签名 - 按照官方PHP SDK的算法"""
        # 排除sign和sign_type字段，以及空值
        filtered_params = {}
        for k, v in params.items():
            if k != 'sign' and k != 'sign_type' and str(v) != '':
                filtered_params[k] = v

        # 按键名排序
        sorted_params = sorted(filtered_params.items())

        # 拼接参数字符串
        sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])

        # 拼接密钥
        sign_str += self.md5_key

        print(f"签名字符串: {sign_str}")

        # 生成MD5签名
        md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        print(f"生成的MD5签名: {md5_hash}")

        return md5_hash
    
    def create_payment(self, pay_type="alipay", money="1.00", name="测试商品"):
        """创建支付订单"""
        # 生成订单号
        out_trade_no = str(int(time.time() * 1000))

        # 构建请求参数
        params = {
            "pid": self.pid,
            "type": pay_type,
            "out_trade_no": out_trade_no,
            "notify_url": "http://www.example.com/notify",
            "return_url": "http://www.example.com/return",
            "name": name,
            "money": money,
            "clientip": "*************",
            "device": "pc",
            "param": "",
            "sign_type": "MD5"
        }

        # 生成签名
        params["sign"] = self.generate_sign(params)

        print(f"请求参数: {json.dumps(params, indent=2, ensure_ascii=False)}")

        try:
            # 发送POST请求，禁用代理
            response = requests.post(
                self.api_url,
                data=params,
                timeout=30,
                proxies={'http': None, 'https': None}  # 禁用代理
            )

            print(f"HTTP状态码: {response.status_code}")
            print(f"原始响应内容: {response.text}")

            # 检查响应状态
            if response.status_code != 200:
                print(f"HTTP请求失败，状态码: {response.status_code}")
                return None

            # 尝试解析JSON
            try:
                result = response.json()
                print(f"解析后的JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
            except json.JSONDecodeError as json_error:
                print(f"JSON解析失败: {json_error}")
                print(f"响应内容不是有效的JSON: {response.text}")
                return None

        except requests.exceptions.RequestException as req_error:
            print(f"网络请求失败: {req_error}")
            return None
        except Exception as e:
            print(f"其他错误: {str(e)}")
            return None

# 测试代码
if __name__ == "__main__":
    api = PaymentAPI()

    print("=== 支付接口测试成功！===")
    print("API地址:", api.api_url)
    print("商户ID:", api.pid)
    print("密钥:", api.md5_key[:8] + "..." + api.md5_key[-8:])
    print()

    # 测试支付宝支付
    print("=== 测试支付宝支付 ===")
    result_alipay = api.create_payment("alipay", "0.01", "测试商品-支付宝")
    if result_alipay and result_alipay.get('code') == 1:
        print(f"✅ 支付宝支付订单创建成功！")
        print(f"   交易号: {result_alipay.get('trade_no')}")
        payurl = result_alipay.get('payurl')
        if not payurl.startswith('http'):
            payurl = f"https://epay.ovovps.cn{payurl}"
        print(f"   支付链接: {payurl}")
    else:
        print("❌ 支付宝支付订单创建失败")

    print()

    # 测试微信支付
    print("=== 测试微信支付 ===")
    result_wxpay = api.create_payment("wxpay", "0.01", "测试商品-微信")
    if result_wxpay and result_wxpay.get('code') == 1:
        print(f"✅ 微信支付订单创建成功！")
        print(f"   交易号: {result_wxpay.get('trade_no')}")
        payurl = result_wxpay.get('payurl')
        if not payurl.startswith('http'):
            payurl = f"https://epay.ovovps.cn{payurl}"
        print(f"   支付链接: {payurl}")
    else:
        print("❌ 微信支付订单创建失败")

    print()

    # 测试QQ支付
    print("=== 测试QQ支付 ===")
    result_qqpay = api.create_payment("qqpay", "0.01", "测试商品-QQ")
    if result_qqpay and result_qqpay.get('code') == 1:
        print(f"✅ QQ支付订单创建成功！")
        print(f"   交易号: {result_qqpay.get('trade_no')}")
        payurl = result_qqpay.get('payurl')
        if not payurl.startswith('http'):
            payurl = f"https://epay.ovovps.cn{payurl}"
        print(f"   支付链接: {payurl}")
    else:
        print("❌ QQ支付订单创建失败")

    print()
    print("=== 测试总结 ===")
    success_count = 0
    if result_alipay and result_alipay.get('code') == 1:
        success_count += 1
    if result_wxpay and result_wxpay.get('code') == 1:
        success_count += 1
    if result_qqpay and result_qqpay.get('code') == 1:
        success_count += 1

    print(f"成功创建 {success_count}/3 个支付订单")
    print("签名算法验证通过！✅")

