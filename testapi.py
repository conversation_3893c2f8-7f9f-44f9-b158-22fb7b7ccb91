
import requests
import hashlib
import time
import json
import urllib.parse

class PaymentAPI:
    def __init__(self):
        self.api_url = "https://epay.ovovps.cn/mapi.php"
        self.pid = "1028"
        self.md5_key = "53X2ZAY3xacC3yD2diHih27xVxvxcfid"
    
    def generate_sign(self, params):
        """生成MD5签名 - 尝试多种方式"""
        # 方式1: 排除sign字段和空值
        filtered_params = {k: v for k, v in params.items() if k != 'sign' and str(v).strip() != ''}
        sorted_params = sorted(filtered_params.items())

        # 尝试方式1: 直接拼接
        sign_str1 = "&".join([f"{k}={v}" for k, v in sorted_params]) + self.md5_key
        md5_hash1 = hashlib.md5(sign_str1.encode('utf-8')).hexdigest()

        # 尝试方式2: URL编码后拼接
        sign_str2 = "&".join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in sorted_params]) + self.md5_key
        md5_hash2 = hashlib.md5(sign_str2.encode('utf-8')).hexdigest()

        # 尝试方式3: 包含空的param字段
        all_params = {k: v for k, v in params.items() if k != 'sign'}
        sorted_all_params = sorted(all_params.items())
        sign_str3 = "&".join([f"{k}={v}" for k, v in sorted_all_params]) + self.md5_key
        md5_hash3 = hashlib.md5(sign_str3.encode('utf-8')).hexdigest()

        print(f"方式1签名字符串: {sign_str1}")
        print(f"方式1 MD5签名: {md5_hash1}")
        print(f"方式2签名字符串: {sign_str2}")
        print(f"方式2 MD5签名: {md5_hash2}")
        print(f"方式3签名字符串: {sign_str3}")
        print(f"方式3 MD5签名: {md5_hash3}")

        # 默认返回方式1
        return md5_hash1
    
    def create_payment_with_sign_method(self, pay_type="alipay", money="1.00", name="测试商品", sign_method=1):
        """创建支付订单，使用指定的签名方法"""
        # 生成订单号
        out_trade_no = str(int(time.time() * 1000))

        # 构建请求参数
        params = {
            "pid": self.pid,
            "type": pay_type,
            "out_trade_no": out_trade_no,
            "notify_url": "http://www.example.com/notify",
            "return_url": "http://www.example.com/return",
            "name": name,
            "money": money,
            "clientip": "*************",
            "device": "pc",
            "param": "",
            "sign_type": "MD5"
        }

        # 根据指定方法生成签名
        if sign_method == 1:
            # 方式1: 排除空值，直接拼接
            filtered_params = {k: v for k, v in params.items() if k != 'sign' and str(v).strip() != ''}
            sorted_params = sorted(filtered_params.items())
            sign_str = "&".join([f"{k}={v}" for k, v in sorted_params]) + self.md5_key
            params["sign"] = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        elif sign_method == 2:
            # 方式2: URL编码后拼接
            filtered_params = {k: v for k, v in params.items() if k != 'sign' and str(v).strip() != ''}
            sorted_params = sorted(filtered_params.items())
            sign_str = "&".join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in sorted_params]) + self.md5_key
            params["sign"] = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        elif sign_method == 3:
            # 方式3: 包含空的param字段
            all_params = {k: v for k, v in params.items() if k != 'sign'}
            sorted_all_params = sorted(all_params.items())
            sign_str = "&".join([f"{k}={v}" for k, v in sorted_all_params]) + self.md5_key
            params["sign"] = hashlib.md5(sign_str.encode('utf-8')).hexdigest()

        print(f"使用签名方式{sign_method}，签名字符串: {sign_str}")
        print(f"生成的签名: {params['sign']}")

        try:
            # 发送POST请求，禁用代理
            response = requests.post(
                self.api_url,
                data=params,
                timeout=30,
                proxies={'http': None, 'https': None}  # 禁用代理
            )

            print(f"HTTP状态码: {response.status_code}")
            print(f"原始响应内容: {response.text}")

            # 检查响应状态
            if response.status_code != 200:
                print(f"HTTP请求失败，状态码: {response.status_code}")
                return None

            # 尝试解析JSON
            try:
                result = response.json()
                print(f"解析后的JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
            except json.JSONDecodeError as json_error:
                print(f"JSON解析失败: {json_error}")
                return None

        except requests.exceptions.RequestException as req_error:
            print(f"网络请求失败: {req_error}")
            return None
        except Exception as e:
            print(f"其他错误: {str(e)}")
            return None

# 测试代码
if __name__ == "__main__":
    api = PaymentAPI()
    
    # 测试支付宝支付
    print("=== 测试支付宝支付 ===")
    result = api.create_payment("alipay", "0.01", "测试商品-支付宝")
    
    # 测试微信支付
    print("\n=== 测试微信支付 ===")
    result = api.create_payment("wxpay", "0.01", "测试商品-微信")

