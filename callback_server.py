#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支付回调服务器
用于接收支付接口的异步通知和同步返回
"""

from flask import Flask, request, jsonify
import hashlib
import time
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('callback.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)

# 支付配置
PAYMENT_CONFIG = {
    'pid': '1028',
    'md5_key': '53X2ZAY3xacC3yD2diHih27xVxvxcfid'
}

# 存储支付结果
payment_results = {}

def generate_sign(params):
    """生成MD5签名 - 按照官方PHP SDK的算法"""
    # 排除sign和sign_type字段，以及空值
    filtered_params = {}
    for k, v in params.items():
        if k != 'sign' and k != 'sign_type' and str(v) != '':
            filtered_params[k] = v
    
    # 按键名排序
    sorted_params = sorted(filtered_params.items())
    
    # 拼接参数字符串
    sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
    
    # 拼接密钥
    sign_str += PAYMENT_CONFIG['md5_key']
    
    # 生成MD5签名
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest()

@app.route('/notify', methods=['POST'])
def notify_callback():
    """异步通知回调"""
    try:
        # 获取POST数据
        data = request.form.to_dict()
        
        logging.info("=== 收到异步通知 ===")
        logging.info(f"通知数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 验证签名
        expected_sign = generate_sign(data)
        received_sign = data.get('sign', '')
        
        if expected_sign == received_sign:
            logging.info("✅ 签名验证通过")
            
            # 存储支付结果
            trade_no = data.get('trade_no')
            out_trade_no = data.get('out_trade_no')
            money = data.get('money')
            pay_type = data.get('type')
            
            payment_results[out_trade_no] = {
                'status': 'success',
                'trade_no': trade_no,
                'out_trade_no': out_trade_no,
                'money': money,
                'type': pay_type,
                'timestamp': time.time(),
                'raw_data': data
            }
            
            logging.info(f"✅ 支付成功！订单号: {out_trade_no}, 交易号: {trade_no}, 金额: {money}元")
            
            # 这里可以添加业务逻辑，比如更新数据库、发送邮件等
            # process_payment_success(out_trade_no, trade_no, money, pay_type)
            
            return "success"
        else:
            logging.error(f"❌ 签名验证失败! 期望: {expected_sign}, 收到: {received_sign}")
            return "fail"
            
    except Exception as e:
        logging.error(f"❌ 处理异步通知时出错: {str(e)}")
        return "fail"

@app.route('/return', methods=['GET'])
def return_callback():
    """同步返回回调"""
    try:
        # 获取GET参数
        data = request.args.to_dict()
        
        logging.info("=== 收到同步返回 ===")
        logging.info(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 验证签名
        expected_sign = generate_sign(data)
        received_sign = data.get('sign', '')
        
        if expected_sign == received_sign:
            logging.info("✅ 同步返回签名验证通过")
            
            trade_no = data.get('trade_no')
            out_trade_no = data.get('out_trade_no')
            money = data.get('money')
            pay_type = data.get('type')
            
            return f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>支付结果</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 50px; }}
                    .success {{ color: green; }}
                    .info {{ margin: 10px 0; }}
                </style>
            </head>
            <body>
                <h2 class="success">✅ 支付成功！</h2>
                <div class="info"><strong>订单号:</strong> {out_trade_no}</div>
                <div class="info"><strong>交易号:</strong> {trade_no}</div>
                <div class="info"><strong>支付金额:</strong> {money} 元</div>
                <div class="info"><strong>支付方式:</strong> {pay_type}</div>
                <div class="info"><strong>支付时间:</strong> {time.strftime('%Y-%m-%d %H:%M:%S')}</div>
                <br>
                <button onclick="window.close()">关闭窗口</button>
            </body>
            </html>
            """
        else:
            logging.error(f"❌ 同步返回签名验证失败! 期望: {expected_sign}, 收到: {received_sign}")
            return """
            <html>
            <head><meta charset="utf-8"><title>支付结果</title></head>
            <body>
                <h2 style="color: red;">❌ 签名验证失败</h2>
                <p>请联系客服处理</p>
            </body>
            </html>
            """
            
    except Exception as e:
        logging.error(f"❌ 处理同步返回时出错: {str(e)}")
        return f"""
        <html>
        <head><meta charset="utf-8"><title>支付结果</title></head>
        <body>
            <h2 style="color: red;">❌ 处理错误</h2>
            <p>{str(e)}</p>
        </body>
        </html>
        """

@app.route('/status/<out_trade_no>', methods=['GET'])
def check_payment_status(out_trade_no):
    """查询支付状态API"""
    if out_trade_no in payment_results:
        result = payment_results[out_trade_no].copy()
        # 移除原始数据，只返回必要信息
        result.pop('raw_data', None)
        return jsonify(result)
    else:
        return jsonify({
            'status': 'pending', 
            'message': '支付未完成或订单不存在',
            'out_trade_no': out_trade_no
        })

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok', 
        'timestamp': time.time(),
        'server': 'Payment Callback Server',
        'version': '1.0'
    })

@app.route('/', methods=['GET'])
def index():
    """首页"""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>支付回调服务器</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 50px; }}
            .endpoint {{ margin: 10px 0; padding: 10px; background: #f5f5f5; }}
        </style>
    </head>
    <body>
        <h1>支付回调服务器</h1>
        <p>服务器运行正常 ✅</p>
        <p>当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <h2>API端点:</h2>
        <div class="endpoint"><strong>POST /notify</strong> - 异步通知回调</div>
        <div class="endpoint"><strong>GET /return</strong> - 同步返回回调</div>
        <div class="endpoint"><strong>GET /status/&lt;订单号&gt;</strong> - 查询支付状态</div>
        <div class="endpoint"><strong>GET /health</strong> - 健康检查</div>
        
        <h2>支付结果统计:</h2>
        <p>已处理支付: {len(payment_results)} 笔</p>
    </body>
    </html>
    """

def process_payment_success(out_trade_no, trade_no, money, pay_type):
    """
    处理支付成功的业务逻辑
    这里可以添加你的业务代码，比如:
    - 更新数据库订单状态
    - 发送邮件通知
    - 调用其他API
    - 等等
    """
    logging.info(f"处理支付成功业务逻辑: {out_trade_no}")
    # 在这里添加你的业务逻辑
    pass

if __name__ == '__main__':
    print("=== 支付回调服务器 ===")
    print("启动地址: http://0.0.0.0:8886")
    print("外网地址: http://turtle.155031.xyz:8886")
    print("异步通知: http://turtle.155031.xyz:8886/notify")
    print("同步返回: http://turtle.155031.xyz:8886/return")
    print("状态查询: http://turtle.155031.xyz:8886/status/<订单号>")
    print("健康检查: http://turtle.155031.xyz:8886/health")
    print("=" * 40)
    
    app.run(host='0.0.0.0', port=8886, debug=False)
