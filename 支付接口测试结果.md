# 支付接口测试结果

## 测试概述
✅ **测试成功！** 所有支付方式均可正常调用

## 接口信息
- **API地址**: https://epay.ovovps.cn/mapi.php
- **商户ID**: 1028
- **签名方式**: MD5
- **支持的支付方式**: alipay（支付宝）、wxpay（微信）、qqpay（QQ钱包）

## 签名算法（重要）
根据官方PHP SDK分析，正确的签名算法为：

1. 排除 `sign` 和 `sign_type` 字段
2. 排除空值字段
3. 按键名进行字典序排序
4. 用 `&` 连接参数：`key1=value1&key2=value2`
5. 直接拼接密钥（不加 `&key=`）
6. 对整个字符串进行MD5加密

**示例签名字符串**:
```
clientip=*************&device=pc&money=0.01&name=测试商品&notify_url=http://www.example.com/notify&out_trade_no=1754221087766&pid=1028&return_url=http://www.example.com/return&type=alipay53X2ZAY3xacC3yD2diHih27xVxvxcfid
```

## 测试结果

### 支付宝支付 ✅
- 状态: 成功
- 交易号: 2025080319380666884
- 支付链接: https://epay.ovovps.cn/pay/cashier/2025080319380666884/?type=alipay

### 微信支付 ✅
- 状态: 成功
- 交易号: 2025080319380718760
- 支付链接: https://epay.ovovps.cn/pay/cashier/2025080319380718760/?type=wxpay

### QQ支付 ✅
- 状态: 成功
- 交易号: 2025080319380792808
- 支付链接: https://epay.ovovps.cn/cashier.php?trade_no=2025080319380792808&sitename=&other=1

## Python代码示例

```python
import requests
import hashlib
import time
import json

class PaymentAPI:
    def __init__(self):
        self.api_url = "https://epay.ovovps.cn/mapi.php"
        self.pid = "1028"
        self.md5_key = "53X2ZAY3xacC3yD2diHih27xVxvxcfid"
    
    def generate_sign(self, params):
        """生成MD5签名 - 按照官方PHP SDK的算法"""
        # 排除sign和sign_type字段，以及空值
        filtered_params = {}
        for k, v in params.items():
            if k != 'sign' and k != 'sign_type' and str(v) != '':
                filtered_params[k] = v
        
        # 按键名排序
        sorted_params = sorted(filtered_params.items())
        
        # 拼接参数字符串
        sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
        
        # 拼接密钥
        sign_str += self.md5_key
        
        # 生成MD5签名
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    
    def create_payment(self, pay_type="alipay", money="1.00", name="测试商品"):
        """创建支付订单"""
        # 生成订单号
        out_trade_no = str(int(time.time() * 1000))
        
        # 构建请求参数
        params = {
            "pid": self.pid,
            "type": pay_type,
            "out_trade_no": out_trade_no,
            "notify_url": "http://www.example.com/notify",
            "return_url": "http://www.example.com/return",
            "name": name,
            "money": money,
            "clientip": "*************",
            "device": "pc",
            "param": "",
            "sign_type": "MD5"
        }
        
        # 生成签名
        params["sign"] = self.generate_sign(params)
        
        try:
            # 发送POST请求
            response = requests.post(
                self.api_url, 
                data=params, 
                timeout=30,
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"请求失败: {str(e)}")
            return None

# 使用示例
if __name__ == "__main__":
    api = PaymentAPI()
    
    # 创建支付宝支付订单
    result = api.create_payment("alipay", "0.01", "测试商品")
    if result and result.get('code') == 1:
        print(f"支付订单创建成功！交易号: {result.get('trade_no')}")
        print(f"支付链接: https://epay.ovovps.cn{result.get('payurl')}")
    else:
        print("支付订单创建失败")
```

## 注意事项

1. **网络问题**: 如果遇到代理或网络超时，使用 `proxies={'http': None, 'https': None}` 禁用代理
2. **签名算法**: 必须严格按照官方SDK的算法实现，否则会返回"MD5签名校验失败"
3. **参数处理**: 空值参数需要排除，但空字符串参数需要包含
4. **字符编码**: 签名字符串使用UTF-8编码

## 问题解决过程

1. **初始问题**: JSON解析失败和网络连接超时
2. **签名问题**: MD5签名校验失败
3. **解决方案**: 参考官方PHP SDK，修正签名算法
4. **最终结果**: 所有支付方式测试通过

---
*测试时间: 2025-08-03*  
*测试状态: ✅ 全部通过*
